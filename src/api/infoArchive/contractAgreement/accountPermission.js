import request from "@/utils/request";
//合同协议账号权限
// 用户列表查询
export function queryPermissionList(data) {
  return request({
    url: "/archive/permission/getUserList",
    method: "get",
    params: data,
  });
}
//状态切换
export function changeStatus(data) {
  return request({
    url: "/archive/permission/updateEnableFlag",
    method: "post",
    data: data,
  });
}
//日志 - 使用通用日志接口
export function queryLog(data) {
  return request({
    url: "/common/log/query",
    method: "post",
    data: data,
  });
}
//查询权限状态
export function getPermissionFlags(data) {
  return request({
    url: "/archive/permission/getPermissionEnableFlag",
    method: "get",
    params: data,
  });
}

// 设置最高权限
export function changeRoot(data) {
  return request({
    url: "/archive/permission/updateMaxEnableFlag",
    method: "post",
    data: data,
  });
}

//配置城市
//获取所有城市树
export function regionTreeList(data) {
  return request({
    url: "/archive/permission/city/getCityTreeList",
    method: "get",
    params: data,
  });
}

//获取已配置城市
export function queryBindRegion(data) {
  return request({
    url: "/archive/permission/city/getBindCityTreeList",
    method: "get",
    params: data,
  });
}
//站点列表查询
export function queryStationList(data) {
  return request({
    url: "/archive/permission/station/getBindStationList",
    method: "post",
    data: data,
  });
}
//已配置的站点
export function queryBindStation(data) {
  return request({
    url: "/archive/permission/station/getBindStationInfo",
    method: "post",
    data: data,
  });
}
//保存站点
export function submitStation(data) {
  return request({
    url: "/archive/permission/station/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//一键配置
export function setAllConfig(data) {
  return request({
    url: "/archive/permission/station/oneClick/save",
    method: "post",
    data: data,
  });
}

//一键清空
export function clearAllConfig(data) {
  return request({
    url: "/archive/permission/station/oneClick/remove",
    method: "post",
    data: data,
  });
}
//配置业务类型
//获取所有业务类型
export function queryBusinessTypeTree(data) {
  return request({
    url: "/archive/permission/business/list",
    method: "get",
    params: data,
  });
}
//获取已配置业务类型
export function queryBusinessTypeChecked(data) {
  return request({
    url: "/archive/permission/business/bindList",
    method: "get",
    params: data,
  });
}
//保存业务类型配置
export function submitBusinessType(data) {
  return request({
    url: "/archive/permission/business/saveOrUpdate",
    method: "post",
    data: data,
  });
}

//配置城市 - 保存城市配置
export function submitCity(data) {
  return request({
    url: "/archive/permission/city/saveOrUpdate",
    method: "post",
    data: data,
  });
}
