import request from "@/utils/request";

export default {
  /**
   * 分页查询合作商列表
   * @param {*} partnerName  合作商
   * @param {*} contact 联系人
   * @param {*} status 状态，0：启用，1：禁用
   * @returns
   */
  queryList(data) {
    return request({
      url: "/ledger/company/list",
      method: "post",
      data,
    });
  },
  /**
   * 编辑或保存
   * @param {*} data
   * @returns
   */
  saveInfo(data) {
    return request({
      url: "/ledger/company/save",
      method: "post",
      data,
    });
  },
  /**
   * 启停状态
   * @param {*} partnerId 主键id	（必填）
   * @param {*} status 状态，0：启用，1：禁用   （必填）
   * @returns
   */
  changeStatus(data) {
    return request({
      url: "/ledger/company/changeStatus",
      method: "post",
      data,
    });
  },
  /**
   * 详情
   * @param {*} partnerId 主键id	（必填）
   * @returns
   */
  getDetail(data) {
    return request({
      url: "/ledger/company/detail",
      method: "get",
      params: data,
    });
  },
  /**
   * 删除
   * @param {*} partnerId 主键id	（必填）
   * @returns
   */
  remove(data) {
    return request({
      url: "/ledger/company/remove",
      method: "get",
      params: data,
    });
  },
  /**
   * 日志
   * @param {*} companyId 主键id	（必填）
   * @returns
   */
  queryLogList(data) {
    return request({
      url: "/ledger/company/log",
      method: "get",
      params: data,
    });
  },
  /**
   * 导出
   * @param {*} partnerName  合作商
   * @param {*} contact 联系人
   * @param {*} status 状态，0：启用，1：禁用
   */
  export(data) {
    return request({
      url: "/ledger/company/export",
      method: "post",
      data: data,
    });
  },
  /**
   * 获取公司类别下拉选项
   */
  queryCompanyType(data) {
    return request({
      url: "/ledger/company/companyCategory",
      method: "get",
      params: data,
    });
  },
  /**
   * 获取公司属性下拉选项
   */
  queryCompanyAttrs(data) {
    return request({
      url: "/ledger/company/companyAttribute",
      method: "get",
      params: data,
    });
  },
  /**
   * 获取公司说明下拉选项
   */
  queryCompanyDesc(data) {
    return request({
      url: "/ledger/company/companyDesc",
      method: "get",
      params: data,
    });
  },
  // 查询已选择的公司标签
  getSelectedTag(data) {
    return request({
      url: "/companyTag/list",
      method: "post",
      data: data,
    });
  },
  // 减少公司标签
  minusCompanyTag(data) {
    return request({
      url: "/companyTag/minus",
      method: "post",
      data: data,
    });
  },
  // 增加公司标签
  addCompanyTag(data) {
    return request({
      url: "/companyTag/add",
      method: "post",
      data: data,
    });
  },
  // 查询标签列表
  getTagList(data) {
    return request({
      url: "/tag/list",
      method: "post",
      data: data,
    });
  },
  /**
   * 获取在线状态下拉选项
   * @returns {Promise} 在线状态选项列表
   */
  getOnlineStatusOptions(data) {
    return request({
      url: "/ledger/company/onlineStatus",
      method: "get",
      params: data,
    });
  },
  /**
   * 获取商户等级下拉选项
   * @returns {Promise} 商户等级选项列表
   */
  getMerchantLevelOptions(data) {
    return request({
      url: "/ledger/company/merchantLevel",
      method: "get",
      params: data,
    });
  },
};
