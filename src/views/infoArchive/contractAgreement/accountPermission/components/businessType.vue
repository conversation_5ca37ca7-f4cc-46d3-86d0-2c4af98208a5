<template>
  <el-card style="margin-bottom: 10px;">
    <div slot="header" style=" display: flex;justify-content: space-between;">
      <div class="card-title-wrap">
        <div class="card-title-line"></div>
        <span>配置业务类型</span>
      </div>
      <el-button @click="handleSubmit" type="primary">保存</el-button>
    </div>
    <el-transfer
      v-model="selectedBusinessTypes"
      :data="allBusinessTypes"
      :titles="['未配置业务类型', '已配置业务类型']"
      filterable
      filter-placeholder="请输入业务类型名称"
    >
    </el-transfer>
  </el-card>
</template>

<script>
import {
  queryBusinessTypeChecked,
  submitBusinessType,
  queryBusinessTypeTree,
} from "@/api/infoArchive/contractAgreement/accountPermission.js";

export default {
  name: "BusinessType",
  props: {
    userId: {
      type: String,
      default: "",
    },
    permissionFlag: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      // 所有业务类型数据，用于穿梭框左侧显示
      allBusinessTypes: [],
      // 已选中的业务类型，用于穿梭框右侧显示
      selectedBusinessTypes: [],
      // 加载状态
      loading: false,
    };
  },
  async created() {
    // 初始化数据
    await this.initData();
  },
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      this.loading = true;
      try {
        // 并行获取所有业务类型和已配置业务类型
        const [allTypesRes, checkedTypesRes] = await Promise.all([
          this.getAllBusinessTypes(),
          this.getCheckedBusinessTypes(),
        ]);

        // 处理所有业务类型数据
        if (allTypesRes?.code === "10000" && Array.isArray(allTypesRes.data)) {
          this.allBusinessTypes = this.formatBusinessTypesForTransfer(
            allTypesRes.data
          );
        }

        // 处理已配置业务类型数据
        if (
          checkedTypesRes?.code === "10000" &&
          Array.isArray(checkedTypesRes.data)
        ) {
          this.selectedBusinessTypes = this.getSelectedKeys(
            checkedTypesRes.data
          );
        }
      } catch (error) {
        console.error("初始化业务类型数据失败:", error);
        this.$message.error("获取业务类型数据失败");
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取所有业务类型
     */
    async getAllBusinessTypes() {
      return await queryBusinessTypeTree({
        userId: this.userId,
      });
    },

    /**
     * 获取已配置的业务类型
     */
    async getCheckedBusinessTypes() {
      return await queryBusinessTypeChecked({
        userId: this.userId,
      });
    },

    /**
     * 格式化业务类型数据为穿梭框需要的格式
     * @param {Array} businessTypes - 业务类型数组 ['业务类型1', '业务类型2']
     * @returns {Array} 格式化后的数据 [{key: '业务类型1', label: '业务类型1'}]
     */
    formatBusinessTypesForTransfer(businessTypes) {
      return businessTypes.map((type) => ({
        key: type, // 使用业务类型名称作为key
        label: type, // 显示的标签
        disabled: false,
      }));
    },

    /**
     * 获取已选中的业务类型key数组
     * @param {Array} checkedTypes - 已配置的业务类型数组 ['业务类型1', '业务类型2']
     * @returns {Array} key数组
     */
    getSelectedKeys(checkedTypes) {
      return checkedTypes || [];
    },

    /**
     * 提交保存业务类型配置
     */
    async handleSubmit() {
      if (!this.userId) {
        this.$message.warning("用户ID不能为空");
        return;
      }

      this.loading = true;
      try {
        const res = await submitBusinessType({
          userId: this.userId,
          businessTypeList: this.selectedBusinessTypes,
        });

        if (res?.code === "10000") {
          this.$message.success("保存成功");
          // 重新获取数据
          await this.initData();
        } else {
          this.$message.error(res?.message || "保存失败");
        }
      } catch (error) {
        console.error("保存业务类型配置失败:", error);
        this.$message.error("保存失败");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-transfer {
  display: flex;
  width: 100%;
  align-items: center;

  .el-transfer-panel {
    flex: 1;
  }

  .el-transfer__buttons {
    width: 28%;
    text-align: center;
  }
}

/deep/
  .el-transfer-panel
  .el-transfer-panel__header
  .el-checkbox
  .el-checkbox__label {
  font-size: 15px;
}
</style>
