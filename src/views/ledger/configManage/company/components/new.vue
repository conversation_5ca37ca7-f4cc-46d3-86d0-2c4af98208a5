<template>
  <el-card>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="140px"
      class="partner-add-form"
    >
      <CommonTitle class="mb10" title="基本信息" />
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="form.companyName"
          maxlength="60"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="公司类别" prop="companyCategory">
        <el-autocomplete
          v-model="form.companyCategory"
          clearable
          placeholder="请输入或选择公司类别，单选"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'queryCompanyType');
            }
          "
          style="width: 100%;"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="公司属性" prop="companyAttrs">
        <el-autocomplete
          v-model="form.companyAttribute"
          clearable
          placeholder="请输入或选择公司属性，单选"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'queryCompanyAttrs');
            }
          "
          style="width: 100%;"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="公司说明" prop="companyDesc">
        <el-autocomplete
          v-model="form.companyDesc"
          clearable
          placeholder="请输入或选择公司说明，单选"
          :fetch-suggestions="
            (queryString, cb) => {
              querySearch(queryString, cb, 'queryCompanyDesc');
            }
          "
          style="width: 100%;"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="公司地址" prop="companyAddress">
        <CustomCascader
          v-model="form.companyAddress"
          placeholder="请选择公司地址"
          :options="areaOptions"
          clearable
          filterable
          :props="{ checkStrictly: true }"
          collapse-tags
          style="width: 100%;"
        ></CustomCascader>
      </el-form-item>
      <el-form-item label="详细地址" prop="detailAddress">
        <el-input
          v-model="form.detailAddress"
          maxlength="250"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="contact">
        <el-input v-model="form.contact" maxlength="60" clearable></el-input>
      </el-form-item>
      <el-form-item label="联系方式" prop="contactTel">
        <el-input v-model="form.contactTel" clearable></el-input>
      </el-form-item>
      <!-- 新增字段：在线状态、上线时间、商户等级、注册户表数 -->
      <el-form-item label="在线状态">
        <el-input
          v-model="form.onlineStatus"
          maxlength="50"
          clearable
          placeholder="请输入在线状态"
        ></el-input>
      </el-form-item>
      <el-form-item label="上线时间">
        <el-date-picker
          v-model="form.onlineDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择上线时间"
          style="width: 100%;"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="商户等级">
        <el-input
          v-model="form.merchantLevel"
          maxlength="50"
          clearable
          placeholder="请输入商户等级"
        ></el-input>
      </el-form-item>
      <el-form-item label="注册户表数">
        <el-input-number
          v-model="form.registerTableCount"
          :min="0"
          :max="99999"
          :precision="0"
          placeholder="请输入注册户表数"
          style="width: 100%;"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="5"
          clearable
          maxlength="1000"
          show-word-limit
          placeholder="请输入具体的说明备注，1000个字符以内"
        >
        </el-input>
      </el-form-item>
      <CommonTitle class="mb10" title="营业执照" />
      <el-form-item label="企业名称">
        <el-input
          v-model="form.enterpriseName"
          maxlength="60"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="统一社会信用代码">
        <el-input
          v-model="form.socialCreditCode"
          maxlength="250"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="企业法人">
        <el-input
          v-model="form.legalPerson"
          maxlength="60"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="营业执照" prop="licenseList">
        <p class="upload-tip">只能上传jpg/jpeg/png文件，且不超过500kb</p>
        <FileUpload
          type="img"
          :limit="3"
          accept=".jpg,.png"
          ref="FileUpload"
          :fileMaxSize="0.5"
          @uploadResult="handleImgUpload"
          :show-file-list="true"
          :allowDrag="false"
        />
      </el-form-item>
      <CommonTitle class="mb10" title="开票信息" />
      <el-form-item label="开户行">
        <el-input v-model="form.bankName" maxlength="60" clearable></el-input>
      </el-form-item>
      <el-form-item label="银行机构代码">
        <el-input v-model="form.bankCode" maxlength="60" clearable></el-input>
      </el-form-item>
      <el-form-item label="账号">
        <el-input
          v-model="form.bankAccount"
          maxlength="60"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="发票寄送地址">
        <el-input
          v-model="form.mailAddress"
          maxlength="250"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="传真">
        <el-input v-model="form.fax" maxlength="30" clearable></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="phoneNumber">
        <el-input v-model="form.phoneNumber" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item label="开户行许可证或盖章版开票信息" prop="licenseList">
        <p class="upload-tip">只能上传jpg/jpeg/png文件，且不超过500kb</p>
        <FileUpload
          type="img"
          :limit="3"
          ref="ticketUpload"
          :fileMaxSize="0.5"
          @uploadResult="handleTicketUpload"
          :show-file-list="true"
          :allowDrag="false"
        />
      </el-form-item> -->
      <CommonTitle class="mb10" title="其他信息" />
      <el-form-item label="电费计价方式">
        <el-input v-model="form.priceType" maxlength="60" clearable></el-input>
      </el-form-item>
      <CommonTitle class="mb10" title="附件上传" />
      <MultiFileUpload
        ref="attachments"
        @uploadSuccess="updateAttachments"
        accept=".png, .jpg, .jpeg, .doc, .docx, .xls, .xlsx, .pdf"
        :maxSize="700"
      >
        <template #customTip>
          支持批量上传，上传格式支持jpg、jpeg、png、doc、docx、xls、xlsx、pdf文件，单个文件700M以内
        </template>
      </MultiFileUpload>
      <el-form-item class="btn-wrap">
        <el-button @click="onCancel" size="medium">取消</el-button>
        <el-button type="primary" @click="onSubmit" size="medium"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import CommonTitle from "@/components/commonTitle";
import { regionData } from "element-china-area-data";
import FileUpload from "@/components/Upload/fileUpload.vue";
import MultiFileUpload from "@/components/MultipleFileUpload/index.vue";
import { validMobile } from "@/utils/validate";
import Api from "@/api/ledger/company.js";
export default {
  name: "ChargingMaintenanceUiNew",
  components: {
    CommonTitle,
    FileUpload,
    MultiFileUpload,
  },

  data() {
    return {
      areaOptions: regionData,
      form: {
        priceType: "",
        phoneNumber: "",
        fax: "",
        mailAddress: "",
        bankAccount: "",
        bankCode: "",
        bankName: "",
        enterpriseName: "",
        companyName: "",
        region: "",
        detailAddress: "",
        contactTel: "",
        contact: "",
        companyAddress: [],
        licenseList: [],
        annexList: [],
        companyDesc: "",
        companyAttribute: "",
        companyCategory: "",
        socialCreditCode: "",
        legalPerson: "",
        // 新增字段
        onlineStatus: "", // 在线状态
        onlineDate: "", // 上线时间
        merchantLevel: "", // 商户等级
        registerTableCount: null, // 注册户表数
      },
      rules: {
        companyName: [{ required: true, message: "请输入合作商名称" }],
        companyCategory: [{ required: true, message: "请输入或选择公司类别" }],
        // companyAddress: [{ required: true, message: "请选择公司地址" }],
        // detailAddress: [{ required: true, message: "请输入详细地址" }],
        contactTel: [
          {
            required: false,
            message: "请输入联系人电话",
            validator: (rule, value, callback) => {
              if (!value || validMobile(value)) {
                callback();
              } else {
                callback(new Error("请输入正确的电话号码"));
              }
            },
          },
        ],
        phoneNumber: [
          {
            required: false,
            message: "请输入电话号码",
            validator: (rule, value, callback) => {
              if (validMobile(value) || value === "") {
                callback();
              } else {
                callback(new Error("请输入正确的电话号码"));
              }
            },
          },
        ],
        // contact: [{ required: true, message: "请输入联系人" }],
        // imgList: [{ required: true, message: "请上传" }],
        // licenseList: [{ required: true, message: "请上传" }],
      },
    };
  },

  mounted() {
    this.companyId = this.$route.query.companyId;
    if (this.companyId) {
      this.getPartnerDetail();
    }
  },

  methods: {
    querySearch(queryString, cb, api) {
      Api[api]({
        status: 0,
        name: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        // this.partnerOptions = res.data;
        cb(result);
      });
    },
    async getPartnerDetail() {
      const res = await Api.getDetail({ companyId: this.companyId });
      if (res.code === "10000") {
        this.form = res.data;
        const { province, city, county, licenseList, annexList } = res.data;
        this.form.licenseList = licenseList;
        this.$refs.FileUpload.setFileList(licenseList);
        this.$refs.attachments.setAttachments(annexList);
        let arr = [];
        province && (arr[0] = province);
        city && (arr[1] = city);
        county && (arr[2] = county);
        this.$set(this.form, "companyAddress", arr);
        // this.form.companyAddress = [province, city, county];
      }
    },
    onSubmit() {
      console.log(this.form);
      this.$refs["form"].validate((valid) => {
        // console.log(object, "object");
        if (valid) {
          this.savePartnerInfo();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getAddress() {},
    async savePartnerInfo() {
      //切割省，市区
      const address = this.form.companyAddress;
      const formData = {
        ...this.form,
        province: address[0],
        city: address[1] || "",
        county: address[2] || "",
        companyId: this.companyId || "",
      };
      const res = await Api.saveInfo(formData);
      if (res.code === "10000") {
        this.$message.success("保存成功");
        this.$router.go(-1);
      } else {
        this.$message.error(res.message);
      }
    },
    onCancel() {
      this.$router.go(-1);
    },
    handleImgUpload(result, msg, fileUploadUrls) {
      if (result) {
        this.form.licenseList = [];
        this.form.licenseList.push(...fileUploadUrls);
      } else {
        this.$message.error(msg);
      }
    },
    // handleTicketUpload(result, msg, fileUploadUrls) {
    //   if (result) {
    //     this.form.licenseList = [];
    //     this.form.licenseList.push(...fileUploadUrls);
    //   } else {
    //     this.$message.error(msg);
    //   }
    // },
    updateAttachments(attachments) {
      this.form.annexList = attachments;
    },
  },
};
</script>

<style lang="less" scoped>
.partner-add-form {
  // width: 50%;
}
.mb10 {
  margin-bottom: 10px;
}
.btn-wrap {
  margin-top: 30px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.upload-tip {
  margin: 0;
  color: #a7a5a5;
}
</style>
